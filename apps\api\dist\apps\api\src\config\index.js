"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.paymentConfig = exports.monitoringConfig = exports.supabaseConfig = exports.externalApisConfig = exports.securityConfig = exports.rateLimitConfig = exports.uploadConfig = exports.emailConfig = exports.jwtConfig = exports.redisConfig = exports.databaseConfig = exports.uploadAllowedTypes = exports.corsOrigins = exports.isTest = exports.isProduction = exports.isDevelopment = exports.config = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
const zod_1 = require("zod");
const path_1 = __importDefault(require("path"));
// Load environment variables
// Try multiple possible locations for .env file
const possibleEnvPaths = [
    path_1.default.resolve(process.cwd(), '.env'),
    path_1.default.resolve(__dirname, '../../.env'),
    path_1.default.resolve(__dirname, '../../../.env'),
];
let envLoaded = false;
for (const envPath of possibleEnvPaths) {
    if (require('fs').existsSync(envPath)) {
        console.log('Loading .env from:', envPath);
        const result = dotenv_1.default.config({ path: envPath });
        if (!result.error) {
            console.log('✅ .env loaded successfully');
            envLoaded = true;
            break;
        }
    }
}
if (!envLoaded) {
    console.log('⚠️ No .env file found, using environment variables');
    dotenv_1.default.config(); // Fallback to default behavior
}
// Environment validation schema
const envSchema = zod_1.z.object({
    // Database
    DATABASE_URL: zod_1.z.string().url('Invalid database URL').optional(),
    // Redis (optional in development)
    REDIS_URL: zod_1.z.string().url('Invalid Redis URL').optional(),
    // JWT
    JWT_SECRET: zod_1.z.string().min(32, 'JWT secret must be at least 32 characters'),
    JWT_REFRESH_SECRET: zod_1.z.string().min(32, 'JWT refresh secret must be at least 32 characters'),
    JWT_EXPIRES_IN: zod_1.z.string().default('15m'),
    JWT_REFRESH_EXPIRES_IN: zod_1.z.string().default('7d'),
    // Server
    PORT: zod_1.z.string().transform(Number).pipe(zod_1.z.number().min(1000).max(65535)).default('3001'),
    NODE_ENV: zod_1.z.enum(['development', 'production', 'test']).default('development'),
    API_VERSION: zod_1.z.string().default('v1'),
    CORS_ORIGIN: zod_1.z.string().default('http://localhost:3000'),
    // Email (optional in development)
    SMTP_HOST: zod_1.z.string().min(1, 'SMTP host is required').optional(),
    SMTP_PORT: zod_1.z.string().transform(Number).pipe(zod_1.z.number().min(1).max(65535)).default('587'),
    SMTP_USER: zod_1.z.string().email('Invalid SMTP user email').optional(),
    SMTP_PASS: zod_1.z.string().min(1, 'SMTP password is required').optional(),
    FROM_EMAIL: zod_1.z.string().email('Invalid from email').optional(),
    FROM_NAME: zod_1.z.string().default('Freela Syria'),
    // File Upload
    UPLOAD_MAX_SIZE: zod_1.z.string().transform(Number).pipe(zod_1.z.number().min(1)).default('10485760'),
    UPLOAD_ALLOWED_TYPES: zod_1.z.string().default('jpg,jpeg,png,gif,webp,pdf,doc,docx,txt'),
    UPLOAD_PATH: zod_1.z.string().default('uploads'),
    CDN_URL: zod_1.z.string().url().optional(),
    // Rate Limiting
    RATE_LIMIT_WINDOW_MS: zod_1.z.string().transform(Number).pipe(zod_1.z.number().min(1)).default('900000'),
    RATE_LIMIT_MAX_REQUESTS: zod_1.z.string().transform(Number).pipe(zod_1.z.number().min(1)).default('100'),
    // Security
    BCRYPT_ROUNDS: zod_1.z.string().transform(Number).pipe(zod_1.z.number().min(10).max(15)).default('12'),
    SESSION_SECRET: zod_1.z.string().min(32, 'Session secret must be at least 32 characters'),
    // External APIs
    OPENAI_API_KEY: zod_1.z.string().optional(),
    OPENROUTER_API_KEY: zod_1.z.string().optional(),
    GOOGLE_MAPS_API_KEY: zod_1.z.string().optional(),
    // Supabase
    SUPABASE_URL: zod_1.z.string().url('Invalid Supabase URL').optional(),
    SUPABASE_ANON_KEY: zod_1.z.string().optional(),
    SUPABASE_SERVICE_ROLE_KEY: zod_1.z.string().optional(),
    // Monitoring
    LOG_LEVEL: zod_1.z.enum(['error', 'warn', 'info', 'debug']).default('info'),
    SENTRY_DSN: zod_1.z.string().optional(),
    // Payment
    STRIPE_SECRET_KEY: zod_1.z.string().optional(),
    STRIPE_WEBHOOK_SECRET: zod_1.z.string().optional(),
    PAYPAL_CLIENT_ID: zod_1.z.string().optional(),
    PAYPAL_CLIENT_SECRET: zod_1.z.string().optional(),
});
// Validate environment variables
const envValidation = envSchema.safeParse(process.env);
if (!envValidation.success) {
    console.error('❌ Invalid environment variables:');
    console.error(envValidation.error.format());
    process.exit(1);
}
exports.config = envValidation.data;
// Derived configurations
exports.isDevelopment = exports.config.NODE_ENV === 'development';
exports.isProduction = exports.config.NODE_ENV === 'production';
exports.isTest = exports.config.NODE_ENV === 'test';
// CORS origins array
exports.corsOrigins = exports.config.CORS_ORIGIN.split(',').map(origin => origin.trim());
// Upload allowed types array
exports.uploadAllowedTypes = exports.config.UPLOAD_ALLOWED_TYPES.split(',').map(type => type.trim());
// Database configuration
exports.databaseConfig = {
    url: exports.config.DATABASE_URL,
};
// Redis configuration
exports.redisConfig = {
    url: exports.config.REDIS_URL || 'redis://localhost:6379',
    enabled: !!exports.config.REDIS_URL,
};
// JWT configuration
exports.jwtConfig = {
    secret: exports.config.JWT_SECRET,
    refreshSecret: exports.config.JWT_REFRESH_SECRET,
    expiresIn: exports.config.JWT_EXPIRES_IN,
    refreshExpiresIn: exports.config.JWT_REFRESH_EXPIRES_IN,
};
// Email configuration
exports.emailConfig = {
    host: exports.config.SMTP_HOST || 'localhost',
    port: exports.config.SMTP_PORT,
    user: exports.config.SMTP_USER || '<EMAIL>',
    pass: exports.config.SMTP_PASS || 'test-password',
    from: {
        email: exports.config.FROM_EMAIL || '<EMAIL>',
        name: exports.config.FROM_NAME,
    },
    enabled: !!(exports.config.SMTP_HOST && exports.config.SMTP_USER && exports.config.SMTP_PASS && exports.config.FROM_EMAIL),
};
// File upload configuration
exports.uploadConfig = {
    maxSize: exports.config.UPLOAD_MAX_SIZE,
    allowedTypes: exports.uploadAllowedTypes,
    path: exports.config.UPLOAD_PATH,
    cdnUrl: exports.config.CDN_URL,
};
// Rate limiting configuration
exports.rateLimitConfig = {
    windowMs: exports.config.RATE_LIMIT_WINDOW_MS,
    maxRequests: exports.config.RATE_LIMIT_MAX_REQUESTS,
};
// Security configuration
exports.securityConfig = {
    bcryptRounds: exports.config.BCRYPT_ROUNDS,
    sessionSecret: exports.config.SESSION_SECRET,
};
// External APIs configuration
exports.externalApisConfig = {
    openai: {
        apiKey: exports.config.OPENAI_API_KEY,
    },
    openrouter: {
        apiKey: exports.config.OPENROUTER_API_KEY,
    },
    googleMaps: {
        apiKey: exports.config.GOOGLE_MAPS_API_KEY,
    },
};
// Supabase configuration
exports.supabaseConfig = {
    url: exports.config.SUPABASE_URL,
    anonKey: exports.config.SUPABASE_ANON_KEY,
    serviceRoleKey: exports.config.SUPABASE_SERVICE_ROLE_KEY,
};
// Monitoring configuration
exports.monitoringConfig = {
    logLevel: exports.config.LOG_LEVEL,
    sentryDsn: exports.config.SENTRY_DSN,
};
// Payment configuration
exports.paymentConfig = {
    stripe: {
        secretKey: exports.config.STRIPE_SECRET_KEY,
        webhookSecret: exports.config.STRIPE_WEBHOOK_SECRET,
    },
    paypal: {
        clientId: exports.config.PAYPAL_CLIENT_ID,
        clientSecret: exports.config.PAYPAL_CLIENT_SECRET,
    },
};
exports.default = exports.config;
//# sourceMappingURL=index.js.map