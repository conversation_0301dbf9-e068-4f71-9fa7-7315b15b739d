const express = require('express');
const http = require('http');

console.log('🧪 Testing minimal Express server...\n');

const app = express();

// Basic middleware
app.use(express.json());

// Health endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    message: 'Minimal server is working'
  });
});

// Test endpoint
app.get('/test', (req, res) => {
  res.json({
    message: 'Test endpoint working',
    timestamp: new Date().toISOString()
  });
});

// Create HTTP server
const server = http.createServer(app);

// Error handling
server.on('error', (error) => {
  console.log('❌ Server error:', error.message);
  console.log('Error code:', error.code);
});

// Start server
const PORT = 3002; // Use different port to avoid conflicts
server.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ Minimal server running on port ${PORT}`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
  console.log(`🧪 Test endpoint: http://localhost:${PORT}/test`);
  
  // Test the server immediately
  setTimeout(testServer, 1000);
});

function testServer() {
  console.log('\n🧪 Testing server endpoints...');
  
  const http = require('http');
  
  // Test health endpoint
  http.get(`http://localhost:${PORT}/health`, (res) => {
    let data = '';
    res.on('data', (chunk) => data += chunk);
    res.on('end', () => {
      console.log('✅ Health endpoint test passed');
      console.log('Response:', data);
      
      // Test another endpoint
      http.get(`http://localhost:${PORT}/test`, (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
          console.log('✅ Test endpoint passed');
          console.log('Response:', data);
          console.log('\n🎉 Minimal server test completed successfully!');
          process.exit(0);
        });
      }).on('error', (err) => {
        console.log('❌ Test endpoint failed:', err.message);
        process.exit(1);
      });
    });
  }).on('error', (err) => {
    console.log('❌ Health endpoint failed:', err.message);
    process.exit(1);
  });
}
