"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/[...nextauth]";
exports.ids = ["pages/api/auth/[...nextauth]"];
exports.modules = {

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/providers/google":
/*!*********************************************!*\
  !*** external "next-auth/providers/google" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/google");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\auth\\[...nextauth].ts */ \"(api)/./src/pages/api/auth/[...nextauth].ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/[...nextauth]\",\n        pathname: \"/api/auth/[...nextauth]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/[...nextauth].ts":
/*!*********************************************!*\
  !*** ./src/pages/api/auth/[...nextauth].ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"next-auth/providers/google\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// import { PrismaAdapter } from '@next-auth/prisma-adapter';\n// import { prisma } from '@freela/database';\n// import { Prisma } from '@prisma/client';\nconst authOptions = {\n    // adapter: PrismaAdapter(prisma), // Temporarily disabled for testing\n    providers: [\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1___default()({\n            clientId: process.env.GOOGLE_CLIENT_ID || \"\",\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET || \"\",\n            authorization: {\n                params: {\n                    prompt: \"consent\",\n                    access_type: \"offline\",\n                    response_type: \"code\"\n                }\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/\",\n        error: \"/auth/error\"\n    },\n    callbacks: {\n        async signIn ({ user, account, profile }) {\n            try {\n                console.log(\"\\uD83D\\uDD10 SignIn callback triggered:\", {\n                    user: user.email,\n                    provider: account?.provider,\n                    profile: profile?.name\n                });\n                if (account?.provider === \"google\") {\n                    console.log(\"✅ Google OAuth sign-in successful for:\", user.email);\n                    // For now, we'll skip database operations and just allow sign-in\n                    // TODO: Re-enable database operations once connection is fixed\n                    return true;\n                }\n                return true;\n            } catch (error) {\n                console.error(\"❌ Error during sign in:\", error);\n                return false;\n            }\n        },\n        async jwt ({ token, user, account, profile, trigger }) {\n            console.log(\"\\uD83D\\uDD11 JWT callback triggered:\", {\n                hasUser: !!user,\n                tokenEmail: token.email,\n                userEmail: user?.email,\n                trigger\n            });\n            if (user) {\n                console.log(\"\\uD83D\\uDC64 Setting up JWT token for new user:\", user.email);\n                // For testing, we'll set default values without database lookup\n                token.id = user.id || user.email || \"temp-id\"; // Use email as fallback ID\n                token.role = \"CLIENT\"; // Default role\n                token.status = \"ACTIVE\";\n                token.language = \"ar\";\n                token.firstName = user.name?.split(\" \")[0] || \"User\";\n                token.lastName = user.name?.split(\" \").slice(1).join(\" \") || \"\";\n                token.avatar = user.image || null;\n                token.hasCompletedOnboarding = false; // Always false for new users\n                console.log(\"✅ JWT token configured:\", {\n                    id: token.id,\n                    role: token.role,\n                    hasCompletedOnboarding: token.hasCompletedOnboarding\n                });\n            }\n            // Handle onboarding completion updates\n            if (trigger === \"update\") {\n                console.log(\"\\uD83D\\uDD04 JWT token update triggered\");\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            console.log(\"\\uD83D\\uDCCB Session callback triggered:\", {\n                tokenEmail: token.email,\n                sessionEmail: session.user?.email,\n                hasCompletedOnboarding: token.hasCompletedOnboarding\n            });\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.status = token.status;\n                session.user.language = token.language;\n                session.user.firstName = token.firstName;\n                session.user.lastName = token.lastName;\n                session.user.avatar = token.avatar;\n                session.user.hasCompletedOnboarding = token.hasCompletedOnboarding;\n                console.log(\"✅ Session configured:\", {\n                    id: session.user.id,\n                    role: session.user.role,\n                    hasCompletedOnboarding: session.user.hasCompletedOnboarding\n                });\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl, token }) {\n            // Handle role-based redirects after successful authentication\n            try {\n                console.log(\"\\uD83D\\uDD04 NextAuth redirect called with:\", {\n                    url,\n                    baseUrl,\n                    hasToken: !!token\n                });\n                // Check if this is a post-authentication callback\n                if (url.includes(\"/api/auth/callback\") || url === baseUrl || url === `${baseUrl}/`) {\n                    console.log(\"\\uD83D\\uDD04 Post-authentication callback detected\");\n                    // CRITICAL FIX: Always redirect to AI onboarding first for proper flow\n                    // The AI onboarding page will handle the logic for completed users\n                    console.log(\"\\uD83E\\uDD16 Redirecting ALL authenticated users to AI onboarding for proper flow handling\");\n                    return `${baseUrl}/ai-onboarding`;\n                }\n                // Handle relative URLs\n                if (url.startsWith(\"/\")) {\n                    const fullUrl = `${baseUrl}${url}`;\n                    console.log(\"\\uD83D\\uDD04 Converting relative URL to absolute:\", fullUrl);\n                    return fullUrl;\n                }\n                // Handle same-origin URLs\n                if (url.startsWith(baseUrl)) {\n                    console.log(\"\\uD83D\\uDD04 Same-origin URL redirect:\", url);\n                    return url;\n                }\n                // Default fallback - redirect to landing page for safety\n                console.log(\"\\uD83C\\uDFE0 Fallback redirect to landing page\");\n                return `${baseUrl}/?auth=success`;\n            } catch (error) {\n                console.error(\"❌ Redirect error:\", error);\n                return `${baseUrl}/?auth=error`;\n            }\n        }\n    },\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    events: {\n        async signIn ({ user, account }) {\n            console.log(`🎉 User ${user.email} signed in with ${account?.provider}`);\n        // TODO: Re-enable database operations once connection is fixed\n        },\n        async signOut ({ token }) {\n            console.log(`👋 User ${token?.email} signed out`);\n        }\n    },\n    debug: \"development\" === \"development\"\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/[...nextauth].ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();