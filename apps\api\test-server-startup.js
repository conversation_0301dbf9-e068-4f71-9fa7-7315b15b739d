#!/usr/bin/env node

/**
 * Test script to verify API server startup
 * This script tests the server initialization without starting the full server
 */

require('dotenv').config({ path: './.env' });

console.log('🧪 Testing API Server Startup...\n');

// Test 1: Environment Variables
console.log('1️⃣ Testing Environment Variables:');
console.log('   - SUPABASE_URL:', process.env.SUPABASE_URL ? '✅ configured' : '❌ not configured');
console.log('   - SUPABASE_ANON_KEY:', process.env.SUPABASE_ANON_KEY ? '✅ configured' : '❌ not configured');
console.log('   - SUPABASE_SERVICE_KEY:', process.env.SUPABASE_SERVICE_KEY ? '✅ configured' : '❌ not configured');
console.log('   - OPENROUTER_API_KEY:', process.env.OPENROUTER_API_KEY ? '✅ configured' : '❌ not configured');
console.log('   - REDIS_URL:', process.env.REDIS_URL ? '✅ configured' : '⚠️ disabled (optional)');
console.log('   - JWT_SECRET:', process.env.JWT_SECRET ? '✅ configured' : '❌ not configured');

// Test 2: Configuration Loading
console.log('\n2️⃣ Testing Configuration Loading:');
try {
  const { config, redisConfig, supabaseConfig } = require('./dist/apps/api/src/config');
  console.log('   - Config loading: ✅ success');
  console.log('   - Redis enabled:', redisConfig.enabled ? '✅ yes' : '⚠️ no (optional)');
  console.log('   - Supabase URL:', supabaseConfig.url ? '✅ configured' : '❌ not configured');
} catch (error) {
  console.log('   - Config loading: ❌ failed');
  console.log('   - Error:', error.message);
  process.exit(1);
}

// Test 3: Database Connection
console.log('\n3️⃣ Testing Database Connection:');
async function testDatabase() {
  try {
    const { connectDatabase, checkDatabaseHealth } = require('@freela/database');
    
    await connectDatabase();
    console.log('   - Database connection: ✅ success');
    
    const health = await checkDatabaseHealth();
    console.log('   - Database health:', health.status === 'healthy' ? '✅ healthy' : '❌ unhealthy');
    if (health.latency) {
      console.log('   - Database latency:', `${health.latency}ms`);
    }
  } catch (error) {
    console.log('   - Database connection: ❌ failed');
    console.log('   - Error:', error.message);
  }
}

// Test 4: Redis Connection (optional)
console.log('\n4️⃣ Testing Redis Connection:');
async function testRedis() {
  try {
    const { redis } = require('./dist/apps/api/src/utils/redis');
    const { redisConfig } = require('./dist/apps/api/src/config');
    
    if (!redisConfig.enabled) {
      console.log('   - Redis connection: ⚠️ disabled (optional)');
      return;
    }
    
    await redis.connect();
    console.log('   - Redis connection:', redis.isReady() ? '✅ success' : '❌ failed');
  } catch (error) {
    console.log('   - Redis connection: ❌ failed (optional)');
    console.log('   - Error:', error.message);
  }
}

// Test 5: App Initialization
console.log('\n5️⃣ Testing App Initialization:');
async function testAppInit() {
  try {
    const App = require('./dist/apps/api/src/app').default;
    
    const app = new App();
    await app.initialize();
    
    console.log('   - App initialization: ✅ success');
    console.log('   - Express app created: ✅ success');
    
    return app;
  } catch (error) {
    console.log('   - App initialization: ❌ failed');
    console.log('   - Error:', error.message);
    throw error;
  }
}

// Run all tests
async function runTests() {
  try {
    await testDatabase();
    await testRedis();
    const app = await testAppInit();
    
    console.log('\n🎉 All tests passed! Server should start successfully.');
    console.log('\n📝 Next steps:');
    console.log('   1. Run: npm run dev');
    console.log('   2. Test endpoints at: http://localhost:3001');
    console.log('   3. Check API docs at: http://localhost:3001/api/v1/docs');
    
  } catch (error) {
    console.log('\n❌ Tests failed. Please fix the issues above before starting the server.');
    process.exit(1);
  }
}

runTests();
