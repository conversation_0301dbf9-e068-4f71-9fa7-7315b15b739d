// Test API startup to identify issues
console.log('🧪 Testing API startup...');

try {
  // Test basic imports
  console.log('1️⃣ Testing basic imports...');
  const express = require('express');
  console.log('✅ Express imported');

  const { dbService } = require('./packages/database/dist/index.js');
  console.log('✅ Database service imported');

  // Test environment variables
  console.log('\n2️⃣ Testing environment variables...');
  require('dotenv').config({ path: './apps/api/.env' });
  
  const requiredEnvVars = [
    'NODE_ENV',
    'PORT',
    'JWT_SECRET',
    'JWT_REFRESH_SECRET',
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_KEY'
  ];

  const missingVars = [];
  requiredEnvVars.forEach(varName => {
    if (!process.env[varName]) {
      missingVars.push(varName);
    } else {
      console.log(`✅ ${varName}: ${varName.includes('SECRET') || varName.includes('KEY') ? '[HIDDEN]' : process.env[varName]}`);
    }
  });

  if (missingVars.length > 0) {
    console.log('\n❌ Missing environment variables:', missingVars);
  } else {
    console.log('\n✅ All required environment variables are set');
  }

  // Test database connection
  console.log('\n3️⃣ Testing database connection...');
  dbService.countUsers().then(count => {
    console.log('✅ Database connection successful, user count:', count);
  }).catch(error => {
    console.log('❌ Database connection failed:', error.message);
  });

  console.log('\n🎉 Basic API startup test completed!');

} catch (error) {
  console.error('❌ API startup test failed:', error.message);
  console.error('Stack:', error.stack);
}
