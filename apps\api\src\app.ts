import express, { Application, Request, Response } from 'express';
import { config, isDevelopment } from './config';
import { logger, requestLogger } from './utils/logger';
import { connectDatabase, disconnectDatabase, checkDatabaseHealth } from '@freela/database';
import { redis } from './utils/redis';
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

// Middleware imports
import securityMiddleware from './middleware/security';
import { errorHand<PERSON>, notFoundHandler } from './middleware/error';

// Route imports
import authRoutes from './routes/auth';
import aiRoutes from './routes/ai';
import onboardingRoutes from './routes/onboarding';

class App {
  public app: Application;

  constructor() {
    this.app = express();
  }

  private initializeMiddleware(): void {
    // Trust proxy for accurate IP addresses
    this.app.set('trust proxy', 1);

    // Security middleware
    this.app.use(securityMiddleware.requestId);
    this.app.use(securityMiddleware.securityHeaders);
    this.app.use(securityMiddleware.helmet);
    this.app.use(securityMiddleware.cors);
    this.app.use(securityMiddleware.compression);
    this.app.use(securityMiddleware.suspiciousActivityDetection);

    // Rate limiting
    this.app.use(securityMiddleware.rateLimit);

    // Request parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging
    this.app.use(requestLogger);

    // Health check endpoint (before rate limiting)
    this.app.get('/health', this.healthCheck);
  }

  private initializeRoutes(): void {
    const apiVersion = config.API_VERSION;

    // API routes
    this.app.use(`/api/${apiVersion}/auth`, authRoutes);
    this.app.use(`/api/${apiVersion}/ai`, aiRoutes);
    this.app.use(`/api/${apiVersion}/onboarding`, onboardingRoutes);

    // Simple test endpoint
    this.app.get(`/api/${apiVersion}/test-connection`, (req, res) => {
      res.json({
        success: true,
        message: 'API connection test successful',
        timestamp: new Date().toISOString(),
        supabase: {
          url: process.env.SUPABASE_URL ? 'configured' : 'not configured',
          anonKey: process.env.SUPABASE_ANON_KEY ? 'configured' : 'not configured'
        },
        openrouter: {
          apiKey: process.env.OPENROUTER_API_KEY ? 'configured' : 'not configured'
        }
      });
    });

    // Health check endpoint
    this.app.get('/health', this.healthCheck);

    // Root endpoint
    this.app.get('/', (req: Request, res: Response) => { // Use imported Request & Response
      res.json({
        success: true,
        message: 'Freela Syria API Server',
        version: apiVersion,
        timestamp: new Date().toISOString(),
        environment: config.NODE_ENV,
      });
    });

    // API info endpoint
    this.app.get(`/api/${apiVersion}`, (req: Request, res: Response) => { // Use imported Request & Response
      res.json({
        success: true,
        message: 'Freela Syria API',
        version: apiVersion,
        endpoints: {
          test: `/api/${apiVersion}/test-connection`,
          docs: `/api/${apiVersion}/docs`,
          health: '/health',
        },
        timestamp: new Date().toISOString(),
      });
    });
  }

  private initializeSwagger(): void {
    if (!isDevelopment) return;

    const swaggerOptions = {
      definition: {
        openapi: '3.0.0',
        info: {
          title: 'Freela Syria API',
          version: '1.0.0',
          description: 'AI-Powered Freelance Marketplace API for Syrian Experts',
          contact: {
            name: 'Freela Syria Team',
            email: '<EMAIL>',
          },
          license: {
            name: 'MIT',
            url: 'https://opensource.org/licenses/MIT',
          },
        },
        servers: [
          {
            url: `http://localhost:${config.PORT}/api/${config.API_VERSION}`,
            description: 'Development server',
          },
        ],
        components: {
          securitySchemes: {
            bearerAuth: {
              type: 'http',
              scheme: 'bearer',
              bearerFormat: 'JWT',
            },
          },
        },
        security: [
          {
            bearerAuth: [],
          },
        ],
        tags: [
          {
            name: 'Authentication',
            description: 'User authentication and authorization endpoints',
          },
          {
            name: 'Users',
            description: 'User management endpoints',
          },
          {
            name: 'Experts',
            description: 'Expert profile management endpoints',
          },
          {
            name: 'Services',
            description: 'Service management endpoints',
          },
          {
            name: 'Bookings',
            description: 'Booking management endpoints',
          },
          {
            name: 'Payments',
            description: 'Payment processing endpoints',
          },
          {
            name: 'Chat',
            description: 'Messaging and chat endpoints',
          },
          {
            name: 'Admin',
            description: 'Administrative endpoints',
          },
          {
            name: 'AI Onboarding',
            description: 'AI-powered onboarding and conversation endpoints',
          },
        ],
      },
      apis: ['./src/routes/*.ts'], // Path to the API docs
    };

    const swaggerSpec = swaggerJsdoc(swaggerOptions);

    // Swagger UI
    this.app.use(
      `/api/${config.API_VERSION}/docs`,
      swaggerUi.serve,
      swaggerUi.setup(swaggerSpec, {
        explorer: true,
        customCss: '.swagger-ui .topbar { display: none }',
        customSiteTitle: 'Freela Syria API Documentation',
      })
    );

    // Swagger JSON
    this.app.get(`/api/${config.API_VERSION}/docs.json`, (req: Request, res: Response) => { // Use imported Request & Response
      res.setHeader('Content-Type', 'application/json');
      res.send(swaggerSpec);
    });

    logger.info(`Swagger documentation available at http://localhost:${config.PORT}/api/${config.API_VERSION}/docs`);
  }

  private initializeErrorHandling(): void {
    // 404 handler
    this.app.use(notFoundHandler);

    // Global error handler
    this.app.use(errorHandler);
  }

  async initialize(): Promise<void> {
    logger.info('Initializing application...');
    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeSwagger();
    this.initializeErrorHandling();

    // Connect to external services with graceful degradation
    try {
      await connectDatabase();
    } catch (error) {
      logger.warn('⚠️ Database connection failed, continuing without database', { error });
    }

    // Try to connect to Redis, but don't fail if it's not available
    try {
      await redis.connect();
      logger.info('✅ Redis connected successfully');
    } catch (error) {
      logger.warn('⚠️ Redis connection failed, continuing without Redis', { error });
    }

    logger.info('✅ Application initialized successfully.');
  }

  async shutdown(): Promise<void> {
    logger.info('Shutting down application...');
    await disconnectDatabase();
    await redis.disconnect();
    logger.info('✅ Application shutdown successfully.');
  }

  private healthCheck = async (req: Request, res: Response): Promise<void> => {
    try {
      // Check database health with timeout
      let dbHealth: any = { status: 'unhealthy' };
      try {
        const dbHealthPromise = checkDatabaseHealth();
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Database health check timeout')), 5000)
        );
        dbHealth = await Promise.race([dbHealthPromise, timeoutPromise]);
      } catch (error) {
        logger.warn('Database health check failed', { error });
        dbHealth = { status: 'unhealthy' };
      }

      const redisHealth = redis.isReady();

      const healthStatus = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: config.NODE_ENV,
        version: config.API_VERSION,
        services: {
          database: {
            status: dbHealth.status === 'healthy' ? 'ok' : 'error',
          },
          redis: {
            status: redisHealth ? 'ok' : 'error',
          },
        },
      };

      // Always return 200 for health check - services can be degraded
      res.status(200).json(healthStatus);
    } catch (error) {
      logger.error('Health check failed', { error });
      res.status(200).json({
        status: 'error',
        timestamp: new Date().toISOString(),
        message: 'Health check failed',
      });
    }
  };
}

export default App;
