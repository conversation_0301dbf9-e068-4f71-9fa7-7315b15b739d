// Debug script to test App class step by step
const express = require('express');
const http = require('http');

console.log('🔍 Debug: Testing App class step by step...\n');

// Test 1: Basic Express app
console.log('1️⃣ Testing basic Express app...');
const basicApp = express();
basicApp.get('/test1', (req, res) => res.json({ test: 1 }));

const basicServer = http.createServer(basicApp);
basicServer.listen(3003, () => {
  console.log('✅ Basic Express app working on port 3003');
  basicServer.close();
  
  // Test 2: With JSON middleware
  console.log('\n2️⃣ Testing with JSON middleware...');
  const jsonApp = express();
  jsonApp.use(express.json());
  jsonApp.get('/test2', (req, res) => res.json({ test: 2 }));
  
  const jsonServer = http.createServer(jsonApp);
  jsonServer.listen(3003, () => {
    console.log('✅ JSON middleware working');
    jsonServer.close();
    
    // Test 3: With CORS
    console.log('\n3️⃣ Testing with CORS...');
    const cors = require('cors');
    const corsApp = express();
    corsApp.use(cors());
    corsApp.use(express.json());
    corsApp.get('/test3', (req, res) => res.json({ test: 3 }));
    
    const corsServer = http.createServer(corsApp);
    corsServer.listen(3003, () => {
      console.log('✅ CORS middleware working');
      corsServer.close();
      
      // Test 4: Load our actual App class
      console.log('\n4️⃣ Testing our App class...');
      testOurApp();
    });
  });
});

async function testOurApp() {
  try {
    // Import our App class
    console.log('Loading App class...');
    
    // First, let's try to import the compiled version
    const App = require('./dist/apps/api/src/app.js').default;
    console.log('✅ App class loaded');
    
    // Create instance
    console.log('Creating App instance...');
    const app = new App();
    console.log('✅ App instance created');
    
    // Initialize (this might be where the issue is)
    console.log('Initializing App...');
    await app.initialize();
    console.log('✅ App initialized');
    
    // Create server
    console.log('Creating HTTP server with App...');
    const server = http.createServer(app.app);
    console.log('✅ HTTP server created');
    
    // Try to listen
    console.log('Attempting to listen on port 3001...');
    server.listen(3001, '0.0.0.0', () => {
      console.log('✅ Our App is listening on port 3001!');
      
      // Test endpoint
      setTimeout(() => {
        const http = require('http');
        http.get('http://localhost:3001/health', (res) => {
          let data = '';
          res.on('data', (chunk) => data += chunk);
          res.on('end', () => {
            console.log('✅ Health endpoint working:', data);
            server.close();
            console.log('\n🎉 All tests passed! The issue is resolved.');
            process.exit(0);
          });
        }).on('error', (err) => {
          console.log('❌ Health endpoint failed:', err.message);
          server.close();
          process.exit(1);
        });
      }, 1000);
    });
    
    server.on('error', (error) => {
      console.log('❌ Server error:', error.message);
      console.log('Error code:', error.code);
    });
    
  } catch (error) {
    console.log('❌ Error testing our App:', error.message);
    console.log('Stack:', error.stack);
    process.exit(1);
  }
}
