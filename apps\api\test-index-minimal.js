// Minimal version of index.ts to test server startup
const { config } = require('./dist/apps/api/src/config');
const { logger } = require('./dist/apps/api/src/utils/logger');
const App = require('./dist/apps/api/src/app').default;
const { createServer } = require('http');

console.log('🧪 Testing minimal index.ts version...\n');

let server;
let app;

async function startServer() {
  try {
    console.log('1️⃣ Creating App instance...');
    app = new App();
    console.log('✅ App instance created');

    console.log('2️⃣ Initializing App...');
    await app.initialize();
    console.log('✅ App initialized');

    console.log('3️⃣ Creating HTTP server...');
    server = createServer(app.app);
    console.log('✅ HTTP server created');

    console.log('4️⃣ Starting server...');
    server.listen(config.PORT, '0.0.0.0', () => {
      console.log(`✅ Server running on port ${config.PORT}`);
      console.log(`🏥 Health Check: http://localhost:${config.PORT}/health`);
      
      // Test the server
      setTimeout(() => {
        console.log('\n🧪 Testing server...');
        testServer();
      }, 1000);
    });

    server.on('error', (error) => {
      console.log('❌ Server error:', error.message);
      console.log('Error code:', error.code);
    });

    server.on('listening', () => {
      console.log('✅ Server listening event fired');
    });

  } catch (error) {
    console.log('❌ Failed to start server:', error.message);
    console.log('Stack:', error.stack);
    process.exit(1);
  }
}

function testServer() {
  const http = require('http');
  
  http.get(`http://localhost:${config.PORT}/health`, (res) => {
    let data = '';
    res.on('data', (chunk) => data += chunk);
    res.on('end', () => {
      console.log('✅ Health endpoint working:', data);
      
      // Test API endpoint
      http.get(`http://localhost:${config.PORT}/api/v1/test-connection`, (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
          console.log('✅ API endpoint working:', data);
          console.log('\n🎉 Server is fully functional!');
          
          // Keep server running for manual testing
          console.log('Server will keep running for manual testing...');
          console.log('Press Ctrl+C to stop');
        });
      }).on('error', (err) => {
        console.log('❌ API endpoint failed:', err.message);
      });
    });
  }).on('error', (err) => {
    console.log('❌ Health endpoint failed:', err.message);
  });
}

// Start the server
startServer();
