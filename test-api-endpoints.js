// Test script for API endpoints
const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001';

async function testHealthEndpoint() {
  console.log('🏥 Testing Health Endpoint...');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/health`);
    console.log('✅ Health endpoint response:', response.status);
    console.log('📊 Health data:', JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.error('❌ Health endpoint failed:', error.message);
    return false;
  }
}

async function testUserRegistration() {
  console.log('\n👤 Testing User Registration...');
  
  const testUser = {
    email: `test-${Date.now()}@example.com`,
    firstName: 'Test',
    lastName: 'User',
    role: 'CLIENT'
  };

  try {
    const response = await axios.post(`${API_BASE_URL}/test/register`, testUser);
    console.log('✅ Registration successful:', response.status);
    console.log('📊 User data:', JSON.stringify(response.data, null, 2));
    return response.data.data;
  } catch (error) {
    console.error('❌ Registration failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
    return null;
  }
}

async function testUserRetrieval(email) {
  console.log('\n🔍 Testing User Retrieval...');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/test/user/${encodeURIComponent(email)}`);
    console.log('✅ User retrieval successful:', response.status);
    console.log('📊 User data:', JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.error('❌ User retrieval failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
    return false;
  }
}

async function runTests() {
  console.log('🧪 Starting API Endpoint Tests...\n');
  
  // Test 1: Health endpoint
  const healthPassed = await testHealthEndpoint();
  
  if (!healthPassed) {
    console.log('\n❌ Health check failed. Stopping tests.');
    return;
  }

  // Test 2: User registration
  const registeredUser = await testUserRegistration();
  
  if (!registeredUser) {
    console.log('\n❌ User registration failed. Stopping tests.');
    return;
  }

  // Test 3: User retrieval
  const retrievalPassed = await testUserRetrieval(registeredUser.email);
  
  // Summary
  console.log('\n📋 Test Summary:');
  console.log(`   Health Endpoint: ${healthPassed ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   User Registration: ${registeredUser ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   User Retrieval: ${retrievalPassed ? '✅ PASS' : '❌ FAIL'}`);
  
  const allPassed = healthPassed && registeredUser && retrievalPassed;
  console.log(`\n🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (allPassed) {
    console.log('\n🚀 Supabase migration is working correctly!');
    console.log('   - Database service layer is properly mapping column names');
    console.log('   - User creation and retrieval operations are functional');
    console.log('   - Backend API is ready for 80% functionality milestone');
  }
}

// Run tests
runTests().catch(console.error);
