const express = require('express');
const http = require('http');

console.log('🔍 Debug: Testing Express app creation and server binding...\n');

// Step 1: Create Express app
console.log('1️⃣ Creating Express app...');
const app = express();
console.log('✅ Express app created');

// Step 2: Add basic middleware
console.log('2️⃣ Adding middleware...');
app.use(express.json());
console.log('✅ JSON middleware added');

// Step 3: Add routes
console.log('3️⃣ Adding routes...');
app.get('/health', (req, res) => {
  console.log('📥 Health endpoint called');
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    message: 'Debug server working'
  });
});

app.get('/api/v1/test-connection', (req, res) => {
  console.log('📥 Test connection endpoint called');
  res.json({
    success: true,
    message: 'API connection test successful',
    timestamp: new Date().toISOString()
  });
});
console.log('✅ Routes added');

// Step 4: Create HTTP server
console.log('4️⃣ Creating HTTP server...');
const server = http.createServer(app);
console.log('✅ HTTP server created');

// Step 5: Add error handlers
console.log('5️⃣ Adding error handlers...');
server.on('error', (error) => {
  console.log('❌ Server error:', error.message);
  console.log('Error code:', error.code);
  console.log('Error details:', error);
});

server.on('listening', () => {
  const addr = server.address();
  console.log('✅ Server is listening on:', addr);
});
console.log('✅ Error handlers added');

// Step 6: Start server
console.log('6️⃣ Starting server...');
const PORT = 3001;

try {
  server.listen(PORT, '0.0.0.0', () => {
    console.log('✅ Server listen callback called');
    console.log(`🚀 Debug server running on port ${PORT}`);
    console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
    console.log(`🧪 Test endpoint: http://localhost:${PORT}/api/v1/test-connection`);
    
    // Test immediately
    setTimeout(() => {
      console.log('\n🧪 Testing endpoints...');
      testEndpoints();
    }, 1000);
  });
  
  console.log('✅ Server.listen() called successfully');
} catch (error) {
  console.log('❌ Error calling server.listen():', error.message);
}

function testEndpoints() {
  const http = require('http');
  
  console.log('Testing health endpoint...');
  http.get(`http://localhost:${PORT}/health`, (res) => {
    let data = '';
    res.on('data', (chunk) => data += chunk);
    res.on('end', () => {
      console.log('✅ Health endpoint working:', data);
      
      // Test API endpoint
      console.log('Testing API endpoint...');
      http.get(`http://localhost:${PORT}/api/v1/test-connection`, (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
          console.log('✅ API endpoint working:', data);
          console.log('\n🎉 All tests passed!');
          process.exit(0);
        });
      }).on('error', (err) => {
        console.log('❌ API endpoint failed:', err.message);
        process.exit(1);
      });
    });
  }).on('error', (err) => {
    console.log('❌ Health endpoint failed:', err.message);
    process.exit(1);
  });
}
