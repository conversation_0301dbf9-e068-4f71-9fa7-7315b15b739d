// Simple test server for Supabase migration
const express = require('express');

// Load environment variables first
require('dotenv').config({ path: './apps/api/.env' });

console.log('🔧 Starting simple test server...');

const app = express();
app.use(express.json());

// Simple health check
app.get('/health', (req, res) => {
  console.log('📋 Health check requested');
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    message: 'Simple test server is running'
  });
});

// Test database connection
app.get('/test/db', async (req, res) => {
  console.log('🔍 Database test requested');
  
  try {
    const { checkDatabaseHealth } = require('./packages/database/dist/index.js');
    const health = await checkDatabaseHealth();
    
    res.json({
      success: true,
      health,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Database test error:', error.message);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Test user creation
app.post('/test/create-user', async (req, res) => {
  console.log('👤 User creation test requested');
  
  try {
    const { dbService } = require('./packages/database/dist/index.js');
    
    const testUser = {
      email: `test-${Date.now()}@example.com`,
      firstName: 'Test',
      lastName: 'User',
      role: 'CLIENT',
      language: 'ar',
      passwordHash: 'test-hash-' + Date.now(),
      status: 'ACTIVE'
    };

    const user = await dbService.createUser(testUser);
    
    console.log('✅ User created:', user.id);
    
    res.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ User creation error:', error.message);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

const PORT = process.env.PORT || 3002;

app.listen(PORT, () => {
  console.log(`🚀 Simple test server running on port ${PORT}`);
  console.log(`📋 Available endpoints:`);
  console.log(`   GET  http://localhost:${PORT}/health`);
  console.log(`   GET  http://localhost:${PORT}/test/db`);
  console.log(`   POST http://localhost:${PORT}/test/create-user`);
});
