// Working test server for API endpoints
const express = require('express');

// Load environment variables
require('dotenv').config({ path: './apps/api/.env' });

console.log('🚀 Starting working test server...');

const app = express();
app.use(express.json());

// Initialize database service
let dbService;
try {
  const dbModule = require('./packages/database/dist/index.js');
  dbService = dbModule.dbService;
  console.log('✅ Database service loaded');
} catch (error) {
  console.error('❌ Failed to load database service:', error.message);
  process.exit(1);
}

// Health endpoint
app.get('/health', async (req, res) => {
  try {
    console.log('📋 Health check requested');
    const userCount = await dbService.countUsers();
    res.json({
      success: true,
      status: 'healthy',
      database: 'connected',
      userCount,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Health check error:', error.message);
    res.status(500).json({
      success: false,
      status: 'unhealthy',
      error: error.message
    });
  }
});

// Register endpoint
app.post('/api/register', async (req, res) => {
  try {
    console.log('📝 Registration requested');
    
    const { email, firstName, lastName, role = 'CLIENT' } = req.body;
    
    if (!email || !firstName || !lastName) {
      return res.status(400).json({
        success: false,
        error: 'Email, firstName, and lastName are required'
      });
    }

    // Check if user exists
    const existingUser = await dbService.findUserByEmail(email);
    if (existingUser) {
      return res.status(409).json({
        success: false,
        error: 'User already exists'
      });
    }

    // Create user
    const userData = {
      email: email.toLowerCase(),
      firstName,
      lastName,
      role: role.toUpperCase(),
      language: 'ar',
      passwordHash: 'test-hash-' + Date.now(),
      status: 'ACTIVE'
    };

    const user = await dbService.createUser(userData);
    
    console.log('✅ User created:', user.id);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        createdAt: user.createdAt
      }
    });

  } catch (error) {
    console.error('❌ Registration error:', error.message);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      details: error.message
    });
  }
});

// Login endpoint (simplified for testing)
app.post('/api/login', async (req, res) => {
  try {
    console.log('🔐 Login requested');
    
    const { email } = req.body;
    
    if (!email) {
      return res.status(400).json({
        success: false,
        error: 'Email is required'
      });
    }

    const user = await dbService.findUserByEmail(email);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Update last login
    const updatedUser = await dbService.updateUser(user.id, {
      lastLoginAt: new Date()
    });

    console.log('✅ Login successful:', user.id);

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        id: updatedUser.id,
        email: updatedUser.email,
        firstName: updatedUser.firstName,
        lastName: updatedUser.lastName,
        role: updatedUser.role,
        lastLoginAt: updatedUser.lastLoginAt
      }
    });

  } catch (error) {
    console.error('❌ Login error:', error.message);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      details: error.message
    });
  }
});

// Get user profile endpoint
app.get('/api/user/:email', async (req, res) => {
  try {
    console.log('🔍 User profile requested');
    
    const { email } = req.params;
    const user = await dbService.findUserByEmail(email);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    console.log('✅ User profile found:', user.id);

    res.json({
      success: true,
      data: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        hasCompletedOnboarding: user.hasCompletedOnboarding,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt
      }
    });

  } catch (error) {
    console.error('❌ Profile retrieval error:', error.message);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      details: error.message
    });
  }
});

const PORT = process.env.PORT || 3003;

app.listen(PORT, () => {
  console.log(`🎯 Working test server running on port ${PORT}`);
  console.log(`📋 Available endpoints:`);
  console.log(`   GET  http://localhost:${PORT}/health`);
  console.log(`   POST http://localhost:${PORT}/api/register`);
  console.log(`   POST http://localhost:${PORT}/api/login`);
  console.log(`   GET  http://localhost:${PORT}/api/user/:email`);
  console.log(`\n🧪 Ready for testing!`);
});
