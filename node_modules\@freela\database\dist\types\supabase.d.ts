export type Json = string | number | boolean | null | {
    [key: string]: <PERSON><PERSON> | undefined;
} | Json[];
export interface Database {
    public: {
        Tables: {
            users: {
                Row: {
                    id: string;
                    email: string;
                    phone: string | null;
                    first_name: string;
                    last_name: string;
                    avatar: <PERSON><PERSON> | null;
                    role: 'CLIENT' | 'EXPERT' | 'ADMIN';
                    status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'PENDING_VERIFICATION';
                    language: string;
                    location: J<PERSON> | null;
                    email_verified: boolean;
                    phone_verified: boolean;
                    email_verification_token: string | null;
                    password_hash: string;
                    provider: string | null;
                    provider_id: string | null;
                    last_login_at: string | null;
                    created_at: string;
                    updated_at: string;
                };
                Insert: {
                    id?: string;
                    email: string;
                    phone?: string | null;
                    first_name: string;
                    last_name: string;
                    avatar?: Json | null;
                    role: 'CLIENT' | 'EXPERT' | 'ADMIN';
                    status?: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'PENDING_VERIFICATION';
                    language?: string;
                    location?: Json | null;
                    email_verified?: boolean;
                    phone_verified?: boolean;
                    email_verification_token?: string | null;
                    password_hash: string;
                    provider?: string | null;
                    provider_id?: string | null;
                    last_login_at?: string | null;
                    created_at?: string;
                    updated_at?: string;
                };
                Update: {
                    id?: string;
                    email?: string;
                    phone?: string | null;
                    first_name?: string;
                    last_name?: string;
                    avatar?: Json | null;
                    role?: 'CLIENT' | 'EXPERT' | 'ADMIN';
                    status?: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'PENDING_VERIFICATION';
                    language?: string;
                    location?: Json | null;
                    email_verified?: boolean;
                    phone_verified?: boolean;
                    email_verification_token?: string | null;
                    password_hash?: string;
                    provider?: string | null;
                    provider_id?: string | null;
                    last_login_at?: string | null;
                    created_at?: string;
                    updated_at?: string;
                };
            };
            expert_profiles: {
                Row: {
                    id: string;
                    user_id: string;
                    title: Json;
                    description: Json;
                    skills: string[];
                    experience: string | null;
                    hourly_rate: number | null;
                    availability: Json | null;
                    response_time: string | null;
                    completed_projects: number;
                    rating: number;
                    review_count: number;
                    verified: boolean;
                    verification_documents: Json[];
                    created_at: string;
                    updated_at: string;
                };
                Insert: {
                    id?: string;
                    user_id: string;
                    title: Json;
                    description: Json;
                    skills?: string[];
                    experience?: string | null;
                    hourly_rate?: number | null;
                    availability?: Json | null;
                    response_time?: string | null;
                    completed_projects?: number;
                    rating?: number;
                    review_count?: number;
                    verified?: boolean;
                    verification_documents?: Json[];
                    created_at?: string;
                    updated_at?: string;
                };
                Update: {
                    id?: string;
                    user_id?: string;
                    title?: Json;
                    description?: Json;
                    skills?: string[];
                    experience?: string | null;
                    hourly_rate?: number | null;
                    availability?: Json | null;
                    response_time?: string | null;
                    completed_projects?: number;
                    rating?: number;
                    review_count?: number;
                    verified?: boolean;
                    verification_documents?: Json[];
                    created_at?: string;
                    updated_at?: string;
                };
            };
            client_profiles: {
                Row: {
                    id: string;
                    user_id: string;
                    company_name: string | null;
                    company_size: string | null;
                    industry: string | null;
                    projects_posted: number;
                    total_spent: number;
                    rating: number;
                    review_count: number;
                    created_at: string;
                    updated_at: string;
                };
                Insert: {
                    id?: string;
                    user_id: string;
                    company_name?: string | null;
                    company_size?: string | null;
                    industry?: string | null;
                    projects_posted?: number;
                    total_spent?: number;
                    rating?: number;
                    review_count?: number;
                    created_at?: string;
                    updated_at?: string;
                };
                Update: {
                    id?: string;
                    user_id?: string;
                    company_name?: string | null;
                    company_size?: string | null;
                    industry?: string | null;
                    projects_posted?: number;
                    total_spent?: number;
                    rating?: number;
                    review_count?: number;
                    created_at?: string;
                    updated_at?: string;
                };
            };
            service_categories: {
                Row: {
                    id: string;
                    name: Json;
                    slug: string;
                    description: Json | null;
                    icon: string | null;
                    parent_id: string | null;
                    service_count: number;
                    featured: boolean;
                    created_at: string;
                    updated_at: string;
                };
                Insert: {
                    id?: string;
                    name: Json;
                    slug: string;
                    description?: Json | null;
                    icon?: string | null;
                    parent_id?: string | null;
                    service_count?: number;
                    featured?: boolean;
                    created_at?: string;
                    updated_at?: string;
                };
                Update: {
                    id?: string;
                    name?: Json;
                    slug?: string;
                    description?: Json | null;
                    icon?: string | null;
                    parent_id?: string | null;
                    service_count?: number;
                    featured?: boolean;
                    created_at?: string;
                    updated_at?: string;
                };
            };
            services: {
                Row: {
                    id: string;
                    expert_id: string;
                    title: Json;
                    description: Json;
                    category_id: string;
                    subcategory: string | null;
                    tags: string[];
                    images: Json[];
                    pricing: Json;
                    delivery_time: number;
                    revisions: number;
                    requirements: Json[];
                    add_ons: Json[];
                    status: 'DRAFT' | 'PENDING_REVIEW' | 'ACTIVE' | 'PAUSED' | 'REJECTED' | 'ARCHIVED';
                    featured: boolean;
                    rating: number;
                    review_count: number;
                    order_count: number;
                    last_order_at: string | null;
                    seo_slug: string;
                    metadata: Json | null;
                    created_at: string;
                    updated_at: string;
                };
                Insert: {
                    id?: string;
                    expert_id: string;
                    title: Json;
                    description: Json;
                    category_id: string;
                    subcategory?: string | null;
                    tags?: string[];
                    images?: Json[];
                    pricing: Json;
                    delivery_time: number;
                    revisions: number;
                    requirements?: Json[];
                    add_ons?: Json[];
                    status?: 'DRAFT' | 'PENDING_REVIEW' | 'ACTIVE' | 'PAUSED' | 'REJECTED' | 'ARCHIVED';
                    featured?: boolean;
                    rating?: number;
                    review_count?: number;
                    order_count?: number;
                    last_order_at?: string | null;
                    seo_slug: string;
                    metadata?: Json | null;
                    created_at?: string;
                    updated_at?: string;
                };
                Update: {
                    id?: string;
                    expert_id?: string;
                    title?: Json;
                    description?: Json;
                    category_id?: string;
                    subcategory?: string | null;
                    tags?: string[];
                    images?: Json[];
                    pricing?: Json;
                    delivery_time?: number;
                    revisions?: number;
                    requirements?: Json[];
                    add_ons?: Json[];
                    status?: 'DRAFT' | 'PENDING_REVIEW' | 'ACTIVE' | 'PAUSED' | 'REJECTED' | 'ARCHIVED';
                    featured?: boolean;
                    rating?: number;
                    review_count?: number;
                    order_count?: number;
                    last_order_at?: string | null;
                    seo_slug?: string;
                    metadata?: Json | null;
                    created_at?: string;
                    updated_at?: string;
                };
            };
            ai_conversation_sessions: {
                Row: {
                    id: string;
                    user_id: string;
                    session_type: string;
                    user_role: 'CLIENT' | 'EXPERT' | 'ADMIN';
                    language: string;
                    current_step: string | null;
                    status: string;
                    ai_model: string;
                    temperature: number;
                    max_tokens: number;
                    completion_rate: number;
                    steps_completed: string[];
                    total_steps: number;
                    extracted_data: Json;
                    profile_data: Json | null;
                    service_data: Json | null;
                    recommendations: Json[];
                    started_at: string;
                    last_active_at: string;
                    completed_at: string | null;
                    estimated_duration: number | null;
                    actual_duration: number | null;
                    created_at: string;
                    updated_at: string;
                };
                Insert: {
                    id?: string;
                    user_id: string;
                    session_type: string;
                    user_role: 'CLIENT' | 'EXPERT' | 'ADMIN';
                    language?: string;
                    current_step?: string | null;
                    status?: string;
                    ai_model?: string;
                    temperature?: number;
                    max_tokens?: number;
                    completion_rate?: number;
                    steps_completed?: string[];
                    total_steps?: number;
                    extracted_data?: Json;
                    profile_data?: Json | null;
                    service_data?: Json | null;
                    recommendations?: Json[];
                    started_at?: string;
                    last_active_at?: string;
                    completed_at?: string | null;
                    estimated_duration?: number | null;
                    actual_duration?: number | null;
                    created_at?: string;
                    updated_at?: string;
                };
                Update: {
                    id?: string;
                    user_id?: string;
                    session_type?: string;
                    user_role?: 'CLIENT' | 'EXPERT' | 'ADMIN';
                    language?: string;
                    current_step?: string | null;
                    status?: string;
                    ai_model?: string;
                    temperature?: number;
                    max_tokens?: number;
                    completion_rate?: number;
                    steps_completed?: string[];
                    total_steps?: number;
                    extracted_data?: Json;
                    profile_data?: Json | null;
                    service_data?: Json | null;
                    recommendations?: Json[];
                    started_at?: string;
                    last_active_at?: string;
                    completed_at?: string | null;
                    estimated_duration?: number | null;
                    actual_duration?: number | null;
                    created_at?: string;
                    updated_at?: string;
                };
            };
            ai_conversation_messages: {
                Row: {
                    id: string;
                    session_id: string;
                    role: string;
                    content: string;
                    content_arabic: string | null;
                    message_type: string;
                    step_name: string | null;
                    intent: string | null;
                    confidence: number | null;
                    ai_model: string | null;
                    prompt_tokens: number | null;
                    completion_tokens: number | null;
                    total_tokens: number | null;
                    processing_time: number | null;
                    user_feedback: string | null;
                    user_rating: number | null;
                    flagged: boolean;
                    flag_reason: string | null;
                    created_at: string;
                    updated_at: string;
                };
                Insert: {
                    id?: string;
                    session_id: string;
                    role: string;
                    content: string;
                    content_arabic?: string | null;
                    message_type?: string;
                    step_name?: string | null;
                    intent?: string | null;
                    confidence?: number | null;
                    ai_model?: string | null;
                    prompt_tokens?: number | null;
                    completion_tokens?: number | null;
                    total_tokens?: number | null;
                    processing_time?: number | null;
                    user_feedback?: string | null;
                    user_rating?: number | null;
                    flagged?: boolean;
                    flag_reason?: string | null;
                    created_at?: string;
                    updated_at?: string;
                };
                Update: {
                    id?: string;
                    session_id?: string;
                    role?: string;
                    content?: string;
                    content_arabic?: string | null;
                    message_type?: string;
                    step_name?: string | null;
                    intent?: string | null;
                    confidence?: number | null;
                    ai_model?: string | null;
                    prompt_tokens?: number | null;
                    completion_tokens?: number | null;
                    total_tokens?: number | null;
                    processing_time?: number | null;
                    user_feedback?: string | null;
                    user_rating?: number | null;
                    flagged?: boolean;
                    flag_reason?: string | null;
                    created_at?: string;
                    updated_at?: string;
                };
            };
            ai_recommendations: {
                Row: {
                    id: string;
                    session_id: string;
                    user_id: string;
                    type: string;
                    category: string;
                    priority: string;
                    title: string;
                    title_arabic: string | null;
                    description: string;
                    description_arabic: string | null;
                    recommendation_data: Json;
                    action_required: Json | null;
                    expected_impact: string | null;
                    confidence_score: number;
                    market_analysis: Json | null;
                    competitor_analysis: Json | null;
                    success_probability: number | null;
                    status: string;
                    user_feedback: string | null;
                    user_rating: number | null;
                    implemented_at: string | null;
                    viewed_at: string | null;
                    responded_at: string | null;
                    expires_at: string | null;
                    created_at: string;
                    updated_at: string;
                };
                Insert: {
                    id?: string;
                    session_id: string;
                    user_id: string;
                    type: string;
                    category: string;
                    priority?: string;
                    title: string;
                    title_arabic?: string | null;
                    description: string;
                    description_arabic?: string | null;
                    recommendation_data: Json;
                    action_required?: Json | null;
                    expected_impact?: string | null;
                    confidence_score: number;
                    market_analysis?: Json | null;
                    competitor_analysis?: Json | null;
                    success_probability?: number | null;
                    status?: string;
                    user_feedback?: string | null;
                    user_rating?: number | null;
                    implemented_at?: string | null;
                    viewed_at?: string | null;
                    responded_at?: string | null;
                    expires_at?: string | null;
                    created_at?: string;
                    updated_at?: string;
                };
                Update: {
                    id?: string;
                    session_id?: string;
                    user_id?: string;
                    type?: string;
                    category?: string;
                    priority?: string;
                    title?: string;
                    title_arabic?: string | null;
                    description?: string;
                    description_arabic?: string | null;
                    recommendation_data?: Json;
                    action_required?: Json | null;
                    expected_impact?: string | null;
                    confidence_score?: number;
                    market_analysis?: Json | null;
                    competitor_analysis?: Json | null;
                    success_probability?: number | null;
                    status?: string;
                    user_feedback?: string | null;
                    user_rating?: number | null;
                    implemented_at?: string | null;
                    viewed_at?: string | null;
                    responded_at?: string | null;
                    expires_at?: string | null;
                    created_at?: string;
                    updated_at?: string;
                };
            };
        };
        Views: {
            [_ in never]: never;
        };
        Functions: {
            [_ in never]: never;
        };
        Enums: {
            user_role: 'CLIENT' | 'EXPERT' | 'ADMIN';
            user_status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'PENDING_VERIFICATION';
            service_status: 'DRAFT' | 'PENDING_REVIEW' | 'ACTIVE' | 'PAUSED' | 'REJECTED' | 'ARCHIVED';
            booking_status: 'PENDING' | 'ACCEPTED' | 'IN_PROGRESS' | 'DELIVERED' | 'REVISION_REQUESTED' | 'COMPLETED' | 'CANCELLED' | 'DISPUTED' | 'REFUNDED';
            payment_status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED' | 'REFUNDED' | 'DISPUTED' | 'CHARGEBACK';
            payment_method: 'CREDIT_CARD' | 'DEBIT_CARD' | 'PAYPAL' | 'BANK_TRANSFER' | 'MOBILE_WALLET' | 'CRYPTOCURRENCY' | 'CASH';
            notification_type: 'BOOKING_REQUEST' | 'BOOKING_ACCEPTED' | 'BOOKING_REJECTED' | 'BOOKING_COMPLETED' | 'PAYMENT_RECEIVED' | 'MESSAGE_RECEIVED' | 'PROFILE_APPROVED' | 'SERVICE_APPROVED' | 'SYSTEM_ANNOUNCEMENT';
        };
        CompositeTypes: {
            [_ in never]: never;
        };
    };
}
//# sourceMappingURL=supabase.d.ts.map