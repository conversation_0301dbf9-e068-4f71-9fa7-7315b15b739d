const http = require('http');

console.log('Testing direct HTTP connection to localhost:3001...\n');

// Test 1: Basic connection test
function testConnection() {
  return new Promise((resolve, reject) => {
    const req = http.get('http://localhost:3001/health', (res) => {
      console.log('✅ Connection successful!');
      console.log('Status Code:', res.statusCode);
      console.log('Headers:', JSON.stringify(res.headers, null, 2));
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log('Response Body:', data);
        resolve({ status: res.statusCode, data });
      });
    });
    
    req.on('error', (err) => {
      console.log('❌ Connection failed:', err.message);
      console.log('Error code:', err.code);
      reject(err);
    });
    
    req.setTimeout(5000, () => {
      console.log('❌ Connection timeout');
      req.destroy();
      reject(new Error('Timeout'));
    });
  });
}

// Test 2: Check if port is listening
function checkPort() {
  return new Promise((resolve, reject) => {
    const server = http.createServer();
    
    server.listen(3001, (err) => {
      if (err) {
        console.log('✅ Port 3001 is in use (good - our server is running)');
        resolve(true);
      } else {
        console.log('❌ Port 3001 is available (bad - our server is not running)');
        server.close();
        resolve(false);
      }
    });
    
    server.on('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        console.log('✅ Port 3001 is in use (good - our server is running)');
        resolve(true);
      } else {
        console.log('❌ Port check error:', err.message);
        resolve(false);
      }
    });
  });
}

async function runTests() {
  console.log('1️⃣ Checking if port 3001 is in use...');
  const portInUse = await checkPort();
  
  if (portInUse) {
    console.log('\n2️⃣ Testing HTTP connection...');
    try {
      await testConnection();
      console.log('\n🎉 All tests passed! API server is working correctly.');
    } catch (error) {
      console.log('\n❌ Connection test failed:', error.message);
    }
  } else {
    console.log('\n❌ Server is not running on port 3001');
  }
}

runTests();
