const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: './apps/api/.env' });

console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? 'Set' : 'Not set');
console.log('SUPABASE_ANON_KEY:', process.env.SUPABASE_ANON_KEY ? 'Set' : 'Not set');
console.log('SUPABASE_SERVICE_KEY:', process.env.SUPABASE_SERVICE_KEY ? 'Set' : 'Not set');

async function testSupabaseConnection() {
  try {
    // Test with anon key first
    console.log('\n🔄 Testing Supabase connection with anon key...');
    const supabaseAnon = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_ANON_KEY
    );

    // Test basic connection
    const { data, error } = await supabaseAnon
      .from('users')
      .select('count')
      .limit(1);

    if (error) {
      console.log('⚠️ Anon key test result:', error.message);
    } else {
      console.log('✅ Supabase anon connection successful');
    }

    // Test with service role key
    console.log('\n🔄 Testing Supabase connection with service role key...');
    const supabaseService = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_KEY
    );

    const { data: serviceData, error: serviceError } = await supabaseService
      .from('users')
      .select('count')
      .limit(1);

    if (serviceError) {
      console.log('⚠️ Service role test result:', serviceError.message);
    } else {
      console.log('✅ Supabase service role connection successful');
    }

    // Test a simple query on users table
    console.log('\n🔄 Testing users table query...');
    const { data: usersData, error: usersError } = await supabaseService
      .from('users')
      .select('id', { count: 'exact', head: true });

    if (usersError) {
      console.log('⚠️ Users table test result:', usersError.message);
    } else {
      console.log('✅ Users table query successful');
    }

    // Test database service layer
    console.log('\n🔄 Testing database service layer...');
    try {
      const { dbService, checkDatabaseHealth } = require('./packages/database/dist/index.js');

      const health = await checkDatabaseHealth();
      console.log('Database health:', health);

      if (health.status === 'healthy') {
        console.log('✅ Database service layer working correctly');
      } else {
        console.log('❌ Database service layer health check failed');
      }
    } catch (dbError) {
      console.log('❌ Database service layer error:', dbError.message);
    }

  } catch (error) {
    console.error('❌ Supabase connection test failed:', error.message);
    console.error('Error details:', error);
  }
}

testSupabaseConnection();
