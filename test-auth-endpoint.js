// Test authentication endpoint with Supabase
const express = require('express');

// Load environment variables first
require('dotenv').config({ path: './apps/api/.env' });

console.log('🔧 Loading database service...');
let dbService;
try {
  const dbModule = require('./packages/database/dist/index.js');
  dbService = dbModule.dbService;
  console.log('✅ Database service loaded successfully');
  console.log('Available exports:', Object.keys(dbModule));
} catch (error) {
  console.error('❌ Failed to load database service:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
}

const app = express();
app.use(express.json());

// Test endpoint to register a user
app.post('/test/register', async (req, res) => {
  try {
    console.log('📝 Testing user registration...');
    
    const { email, firstName, lastName, role = 'CLIENT' } = req.body;
    
    if (!email || !firstName || !lastName) {
      return res.status(400).json({
        success: false,
        error: 'Email, firstName, and lastName are required'
      });
    }

    // Check if user exists
    const existingUser = await dbService.findUserByEmail(email);
    if (existingUser) {
      return res.status(409).json({
        success: false,
        error: 'User already exists'
      });
    }

    // Create user
    const userData = {
      email: email.toLowerCase(),
      firstName,
      lastName,
      role: role.toUpperCase(),
      language: 'ar',
      passwordHash: 'test-hash-' + Date.now(), // In real app, this would be properly hashed
      status: 'ACTIVE'
    };

    const user = await dbService.createUser(userData);
    
    console.log('✅ User created successfully:', user.id);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        createdAt: user.createdAt
      }
    });

  } catch (error) {
    console.error('❌ Registration error:', error.message);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      details: error.message
    });
  }
});

// Test endpoint to get user by email
app.get('/test/user/:email', async (req, res) => {
  try {
    console.log('🔍 Testing user retrieval...');
    
    const { email } = req.params;
    const user = await dbService.findUserByEmail(email);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    console.log('✅ User found:', user.id);

    res.json({
      success: true,
      data: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        hasCompletedOnboarding: user.hasCompletedOnboarding,
        createdAt: user.createdAt
      }
    });

  } catch (error) {
    console.error('❌ Retrieval error:', error.message);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      details: error.message
    });
  }
});

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    const userCount = await dbService.countUsers();
    res.json({
      success: true,
      status: 'healthy',
      database: 'connected',
      userCount,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      status: 'unhealthy',
      error: error.message
    });
  }
});

const PORT = process.env.PORT || 3001;

app.listen(PORT, () => {
  console.log(`🧪 Test server running on port ${PORT}`);
  console.log(`📋 Test endpoints:`);
  console.log(`   POST http://localhost:${PORT}/test/register`);
  console.log(`   GET  http://localhost:${PORT}/test/user/:email`);
  console.log(`   GET  http://localhost:${PORT}/health`);
  console.log(`\n🔧 Test the migration with:`);
  console.log(`   curl -X POST http://localhost:${PORT}/test/register -H "Content-Type: application/json" -d '{"email":"<EMAIL>","firstName":"Test","lastName":"User","role":"CLIENT"}'`);
});
