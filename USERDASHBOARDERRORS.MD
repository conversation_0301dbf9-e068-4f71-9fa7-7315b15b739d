
 
1 of 3 unhandled errors
Next.js (14.0.3) is outdated (learn more)

Unhandled Runtime Error
Error: Text content does not match server-rendered HTML.

Warning: Text content did not match. Server: "2.450" Client: "2,450"

See more info here: https://nextjs.org/docs/messages/react-hydration-error

Component Stack
p
div
div
div
div
EarningsOverview
div
div
ExpertDashboardPage
Call Stack
checkForUnmatchedText
node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js (32605:0)
didNotMatchHydratedTextInstance
node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js (36008:0)
prepareToHydrateHostTextInstance
node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js (7329:0)
completeWork
node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js (19732:0)
completeUnitOfWork
node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js (25793:0)
performUnitOfWork
node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js (25598:0)
workLoopConcurrent
node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js (25573:0)
renderRootConcurrent
node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js (25529:0)
performConcurrentWorkOnRoot
node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js (24382:0)
workLoop
node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js (261:0)
flushWork
node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js (230:0)
MessagePort.performWorkUntilDeadline
node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js (534:0)

app-index.js:34 Warning: Text content did not match. Server: "2.450" Client: "2,450"
    at p
    at div
    at div
    at div
    at div
    at EarningsOverview
    at div
    at div
    at ExpertDashboardPage
    at StaticGenerationSearchParamsBailoutProvider (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js:15:11)
    at InnerLayoutRouter (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:240:11)
    at RedirectErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js:72:9)
    at RedirectBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js:80:11)
    at NotFoundBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js:62:11)
    at LoadingBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:345:11)
    at ErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js:130:11)
    at InnerScrollAndFocusHandler (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:151:9)
    at ScrollAndFocusHandler (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:226:11)
    at RenderFromTemplateContext (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js:15:44)
    at OuterLayoutRouter (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:355:11)
    at div
    at main
    at div
    at div
    at DashboardLayout (webpack-internal:///(app-pages-browser)/./src/app/dashboard/layout.tsx:16:11)
    at InnerLayoutRouter (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:240:11)
    at RedirectErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js:72:9)
    at RedirectBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js:80:11)
    at NotFoundErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js:54:9)
    at NotFoundBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js:62:11)
    at LoadingBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:345:11)
    at ErrorBoundaryHandler (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js:100:9)
    at ErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js:130:11)
    at InnerScrollAndFocusHandler (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:151:9)
    at ScrollAndFocusHandler (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:226:11)
    at RenderFromTemplateContext (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js:15:44)
    at OuterLayoutRouter (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:355:11)
    at f (webpack-internal:///(app-pages-browser)/../../node_modules/next-themes/dist/index.module.js:8:597)
    at $ (webpack-internal:///(app-pages-browser)/../../node_modules/next-themes/dist/index.module.js:8:348)
    at QueryClientProvider (webpack-internal:///(app-pages-browser)/../../node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js:28:3)
    at Providers (webpack-internal:///(app-pages-browser)/./src/app/providers.tsx:19:11)
    at body
    at html
    at RedirectErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js:72:9)
    at RedirectBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js:80:11)
    at NotFoundErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js:54:9)
    at NotFoundBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js:62:11)
    at DevRootNotFoundBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-not-found-boundary.js:32:11)
    at ReactDevOverlay (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/internal/ReactDevOverlay.js:66:9)
    at HotReload (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/hot-reloader-client.js:295:11)
    at Router (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js:169:11)
    at ErrorBoundaryHandler (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js:100:9)
    at ErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js:130:11)
    at AppRouter (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js:451:13)
    at ServerRoot (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-index.js:128:11)
    at RSCComponent
    at Root (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-index.js:144:11)
window.console.error @ app-index.js:34
app-index.js:34 Warning: An error occurred during hydration. The server HTML was replaced with client content in <#document>.
window.console.error @ app-index.js:34
react-dom.development.js:32605 Uncaught Error: Text content does not match server-rendered HTML.

Warning: Text content did not match. Server: "2.450" Client: "2,450"

See more info here: https://nextjs.org/docs/messages/react-hydration-error
    at checkForUnmatchedText (react-dom.development.js:32605:11)
    at didNotMatchHydratedTextInstance (react-dom.development.js:36008:5)
    at prepareToHydrateHostTextInstance (react-dom.development.js:7329:13)
    at completeWork (react-dom.development.js:19732:17)
    at completeUnitOfWork (react-dom.development.js:25793:14)
    at performUnitOfWork (react-dom.development.js:25598:5)
    at workLoopConcurrent (react-dom.development.js:25573:5)
    at renderRootConcurrent (react-dom.development.js:25529:9)
    at performConcurrentWorkOnRoot (react-dom.development.js:24382:38)
    at workLoop (scheduler.development.js:261:34)
    at flushWork (scheduler.development.js:230:14)
    at MessagePort.performWorkUntilDeadline (scheduler.development.js:534:21)
on-recoverable-error.js:20 Uncaught Error: Text content does not match server-rendered HTML.

Warning: Text content did not match. Server: "2.450" Client: "2,450"

See more info here: https://nextjs.org/docs/messages/react-hydration-error
    at checkForUnmatchedText (react-dom.development.js:32605:11)
    at didNotMatchHydratedTextInstance (react-dom.development.js:36008:5)
    at prepareToHydrateHostTextInstance (react-dom.development.js:7329:13)
    at completeWork (react-dom.development.js:19732:17)
    at completeUnitOfWork (react-dom.development.js:25793:14)
    at performUnitOfWork (react-dom.development.js:25598:5)
    at workLoopConcurrent (react-dom.development.js:25573:5)
    at renderRootConcurrent (react-dom.development.js:25529:9)
    at performConcurrentWorkOnRoot (react-dom.development.js:24382:38)
    at workLoop (scheduler.development.js:261:34)
    at flushWork (scheduler.development.js:230:14)
    at MessagePort.performWorkUntilDeadline (scheduler.development.js:534:21)
on-recoverable-error.js:20 Uncaught Error: Text content does not match server-rendered HTML.

Warning: Text content did not match. Server: "2.450" Client: "2,450"

See more info here: https://nextjs.org/docs/messages/react-hydration-error
    at checkForUnmatchedText (react-dom.development.js:32605:11)
    at didNotMatchHydratedTextInstance (react-dom.development.js:36008:5)
    at prepareToHydrateHostTextInstance (react-dom.development.js:7329:13)
    at completeWork (react-dom.development.js:19732:17)
    at completeUnitOfWork (react-dom.development.js:25793:14)
    at performUnitOfWork (react-dom.development.js:25598:5)
    at workLoopConcurrent (react-dom.development.js:25573:5)
    at renderRootConcurrent (react-dom.development.js:25529:9)
    at performConcurrentWorkOnRoot (react-dom.development.js:24382:38)
    at workLoop (scheduler.development.js:261:34)
    at flushWork (scheduler.development.js:230:14)
    at MessagePort.performWorkUntilDeadline (scheduler.development.js:534:21)
on-recoverable-error.js:20 Uncaught Error: Text content does not match server-rendered HTML.

Warning: Text content did not match. Server: "2.450" Client: "2,450"

See more info here: https://nextjs.org/docs/messages/react-hydration-error
    at checkForUnmatchedText (react-dom.development.js:32605:11)
    at didNotMatchHydratedTextInstance (react-dom.development.js:36008:5)
    at prepareToHydrateHostTextInstance (react-dom.development.js:7329:13)
    at completeWork (react-dom.development.js:19732:17)
    at completeUnitOfWork (react-dom.development.js:25793:14)
    at performUnitOfWork (react-dom.development.js:25598:5)
    at workLoopConcurrent (react-dom.development.js:25573:5)
    at renderRootConcurrent (react-dom.development.js:25529:9)
    at performConcurrentWorkOnRoot (react-dom.development.js:24382:38)
    at workLoop (scheduler.development.js:261:34)
    at flushWork (scheduler.development.js:230:14)
    at MessagePort.performWorkUntilDeadline (scheduler.development.js:534:21)
on-recoverable-error.js:20 Uncaught Error: Hydration failed because the initial UI does not match what was rendered on the server.

Warning: Text content did not match. Server: "2.450" Client: "2,450"

See more info here: https://nextjs.org/docs/messages/react-hydration-error
    at throwOnHydrationMismatch (react-dom.development.js:7076:9)
    at tryToClaimNextHydratableInstance (react-dom.development.js:7105:7)
    at updateHostComponent$1 (react-dom.development.js:16503:5)
    at beginWork$1 (react-dom.development.js:18390:14)
    at beginWork (react-dom.development.js:26741:14)
    at performUnitOfWork (react-dom.development.js:25587:12)
    at workLoopConcurrent (react-dom.development.js:25573:5)
    at renderRootConcurrent (react-dom.development.js:25529:9)
    at performConcurrentWorkOnRoot (react-dom.development.js:24382:38)
    at workLoop (scheduler.development.js:261:34)
    at flushWork (scheduler.development.js:230:14)
    at MessagePort.performWorkUntilDeadline (scheduler.development.js:534:21)
on-recoverable-error.js:20 Uncaught Error: Hydration failed because the initial UI does not match what was rendered on the server.

Warning: Text content did not match. Server: "2.450" Client: "2,450"

See more info here: https://nextjs.org/docs/messages/react-hydration-error
    at throwOnHydrationMismatch (react-dom.development.js:7076:9)
    at tryToClaimNextHydratableInstance (react-dom.development.js:7105:7)
    at updateHostComponent$1 (react-dom.development.js:16503:5)
    at beginWork$1 (react-dom.development.js:18390:14)
    at beginWork (react-dom.development.js:26741:14)
    at performUnitOfWork (react-dom.development.js:25587:12)
    at workLoopConcurrent (react-dom.development.js:25573:5)
    at renderRootConcurrent (react-dom.development.js:25529:9)
    at performConcurrentWorkOnRoot (react-dom.development.js:24382:38)
    at workLoop (scheduler.development.js:261:34)
    at flushWork (scheduler.development.js:230:14)
    at MessagePort.performWorkUntilDeadline (scheduler.development.js:534:21)
on-recoverable-error.js:20 Uncaught Error: Hydration failed because the initial UI does not match what was rendered on the server.

Warning: Text content did not match. Server: "2.450" Client: "2,450"

See more info here: https://nextjs.org/docs/messages/react-hydration-error
    at throwOnHydrationMismatch (react-dom.development.js:7076:9)
    at tryToClaimNextHydratableInstance (react-dom.development.js:7105:7)
    at updateHostComponent$1 (react-dom.development.js:16503:5)
    at beginWork$1 (react-dom.development.js:18390:14)
    at beginWork (react-dom.development.js:26741:14)
    at performUnitOfWork (react-dom.development.js:25587:12)
    at workLoopConcurrent (react-dom.development.js:25573:5)
    at renderRootConcurrent (react-dom.development.js:25529:9)
    at performConcurrentWorkOnRoot (react-dom.development.js:24382:38)
    at workLoop (scheduler.development.js:261:34)
    at flushWork (scheduler.development.js:230:14)
    at MessagePort.performWorkUntilDeadline (scheduler.development.js:534:21)
react-dom.development.js:16451 Uncaught Error: There was an error while hydrating. Because the error happened outside of a Suspense boundary, the entire root will switch to client rendering.
    at updateHostRoot (react-dom.development.js:16451:57)
    at beginWork$1 (react-dom.development.js:18373:14)
    at beginWork (react-dom.development.js:26741:14)
    at performUnitOfWork (react-dom.development.js:25587:12)
    at workLoopSync (react-dom.development.js:25303:5)
    at renderRootSync (react-dom.development.js:25258:7)
    at recoverFromConcurrentError (react-dom.development.js:24475:20)
    at performConcurrentWorkOnRoot (react-dom.development.js:24420:26)
    at workLoop (scheduler.development.js:261:34)
    at flushWork (scheduler.development.js:230:14)
    at MessagePort.performWorkUntilDeadline (scheduler.development.js:534:21)
