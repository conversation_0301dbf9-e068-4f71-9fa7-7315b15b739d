import { createClient } from 'redis';
import { redisConfig } from '../config';
import { logger } from './logger';

type RedisClientType = import('redis').RedisClientType;

class RedisClient {
  private client: RedisClientType;
  private isConnected: boolean;

  constructor() {
    this.client = createClient({
      url: redisConfig.url,
      socket: {
        reconnectStrategy: (retries: number) => {
          if (retries > 10) {
            logger.error('Redis: Maximum reconnection attempts reached');
            return new Error('Redis: Maximum reconnection attempts reached');
          }
          return Math.min(retries * 100, 3000);
        },
      },
    });

    this.isConnected = false;
    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    this.client.on('connect', () => {
      logger.info('Redis: Connecting...');
    });

    this.client.on('ready', () => {
      this.isConnected = true;
      logger.info('Redis: Connected and ready');
    });

    this.client.on('error', (error: Error) => {
      logger.error('Redis: Connection error', { error: error.message });
      this.isConnected = false;
    });

    this.client.on('end', () => {
      this.isConnected = false;
      logger.info('Redis: Connection ended');
    });

    this.client.on('reconnecting', () => {
      logger.info('Redis: Reconnecting...');
    });
  }

  async connect(): Promise<void> {
    if (this.isConnected) {
      logger.info('Redis: Already connected');
      return;
    }

    // Check if Redis is enabled in configuration
    if (!redisConfig.enabled) {
      logger.info('Redis: Disabled in configuration, skipping connection');
      return;
    }

    try {
      logger.info('Redis: Connecting...');
      await Promise.race([
        this.client.connect(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Redis connection timed out after 10 seconds')), 10000)
        ),
      ]);
    } catch (error) {
      if (error instanceof Error) {
        logger.error('Redis: Failed to connect', { error: error.message });
      } else {
        logger.error('Redis: Failed to connect with an unknown error', { error });
      }
      // Don't throw the error, just log it for graceful degradation
    }
  }

  async disconnect(): Promise<void> {
    if (!this.isConnected) {
        return;
    }
    try {
      await this.client.disconnect();
    } catch (error) {
      logger.error('Redis: Failed to disconnect', { error });
    }
  }

  isReady(): boolean {
    return redisConfig.enabled && this.isConnected;
  }

  async get(key: string): Promise<string | null> {
    if (!this.isReady()) return null;
    try {
      return await this.client.get(key);
    } catch (error) {
      logger.error('Redis: GET operation failed', { key, error });
      return null;
    }
  }

  async set(key: string, value: string, ttl?: number): Promise<boolean> {
    if (!this.isReady()) return false;
    try {
      if (ttl) {
        await this.client.setEx(key, ttl, value);
      } else {
        await this.client.set(key, value);
      }
      return true;
    } catch (error) {
      logger.error('Redis: SET operation failed', { key, error });
      return false;
    }
  }

  async del(key: string): Promise<boolean> {
    if (!this.isReady()) return false;
    try {
      const result = await this.client.del(key);
      return result > 0;
    } catch (error) {
      logger.error('Redis: DEL operation failed', { key, error });
      return false;
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result > 0;
    } catch (error) {
      logger.error('Redis: EXISTS operation failed', { key, error });
      return false;
    }
  }

  async expire(key: string, ttl: number): Promise<boolean> {
    try {
      return await this.client.expire(key, ttl);
    } catch (error) {
      logger.error('Redis: EXPIRE operation failed', { key, ttl, error });
      return false;
    }
  }

  // JSON operations
  async getJSON<T>(key: string): Promise<T | null> {
    try {
      const value = await this.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error('Redis: JSON GET operation failed', { key, error });
      return null;
    }
  }

  async setJSON<T>(key: string, value: T, ttl?: number): Promise<boolean> {
    try {
      return await this.set(key, JSON.stringify(value), ttl);
    } catch (error) {
      logger.error('Redis: JSON SET operation failed', { key, error });
      return false;
    }
  }

  // Hash operations
  async hGet(key: string, field: string): Promise<string | null> {
    try {
      const result = await this.client.hGet(key, field);
      return result || null;
    } catch (error) {
      logger.error('Redis: HGET operation failed', { key, field, error });
      return null;
    }
  }

  async hSet(key: string, field: string, value: string): Promise<boolean> {
    try {
      await this.client.hSet(key, field, value);
      return true;
    } catch (error) {
      logger.error('Redis: HSET operation failed', { key, field, error });
      return false;
    }
  }

  async hGetAll(key: string): Promise<Record<string, string> | null> {
    try {
      return await this.client.hGetAll(key);
    } catch (error) {
      logger.error('Redis: HGETALL operation failed', { key, error });
      return null;
    }
  }

  async hDel(key: string, field: string): Promise<boolean> {
    try {
      const result = await this.client.hDel(key, field);
      return result > 0;
    } catch (error) {
      logger.error('Redis: HDEL operation failed', { key, field, error });
      return false;
    }
  }

  // List operations
  async lPush(key: string, value: string): Promise<boolean> {
    try {
      await this.client.lPush(key, value);
      return true;
    } catch (error) {
      logger.error('Redis: LPUSH operation failed', { key, error });
      return false;
    }
  }

  async rPop(key: string): Promise<string | null> {
    try {
      return await this.client.rPop(key);
    } catch (error) {
      logger.error('Redis: RPOP operation failed', { key, error });
      return null;
    }
  }

  // Set operations
  async sAdd(key: string, member: string): Promise<boolean> {
    try {
      const result = await this.client.sAdd(key, member);
      return result > 0;
    } catch (error) {
      logger.error('Redis: SADD operation failed', { key, member, error });
      return false;
    }
  }

  async sIsMember(key: string, member: string): Promise<boolean> {
    try {
      return await this.client.sIsMember(key, member);
    } catch (error) {
      logger.error('Redis: SISMEMBER operation failed', { key, member, error });
      return false;
    }
  }

  async sRem(key: string, member: string): Promise<boolean> {
    try {
      const result = await this.client.sRem(key, member);
      return result > 0;
    } catch (error) {
      logger.error('Redis: SREM operation failed', { key, member, error });
      return false;
    }
  }

  // Utility methods
  async flushAll(): Promise<boolean> {
    try {
      await this.client.flushAll();
      return true;
    } catch (error) {
      logger.error('Redis: FLUSHALL operation failed', { error });
      return false;
    }
  }

  async ping(): Promise<boolean> {
    try {
      const result = await this.client.ping();
      return result === 'PONG';
    } catch (error) {
      logger.error('Redis: PING operation failed', { error });
      return false;
    }
  }

  // Get the raw client for advanced operations
  getClient(): RedisClientType {
    return this.client;
  }
}

// Create singleton instance
export const redis = new RedisClient();

// Session management helpers
export const sessionHelpers = {
  async setSession(sessionId: string, userId: string, data: any, ttl: number = 86400): Promise<boolean> {
    const sessionData = {
      userId,
      data,
      createdAt: new Date().toISOString(),
    };
    return await redis.setJSON(`session:${sessionId}`, sessionData, ttl);
  },

  async getSession(sessionId: string): Promise<{ userId: string; data: any; createdAt: string } | null> {
    return await redis.getJSON(`session:${sessionId}`);
  },

  async deleteSession(sessionId: string): Promise<boolean> {
    return await redis.del(`session:${sessionId}`);
  },

  async refreshSession(sessionId: string, ttl: number = 86400): Promise<boolean> {
    return await redis.expire(`session:${sessionId}`, ttl);
  },
};

// Cache helpers
export const cacheHelpers = {
  async cacheUserData(userId: string, data: any, ttl: number = 3600): Promise<boolean> {
    return await redis.setJSON(`user:${userId}`, data, ttl);
  },

  async getUserData(userId: string): Promise<any | null> {
    return await redis.getJSON(`user:${userId}`);
  },

  async invalidateUserCache(userId: string): Promise<boolean> {
    return await redis.del(`user:${userId}`);
  },

  async cacheServiceData(serviceId: string, data: any, ttl: number = 1800): Promise<boolean> {
    return await redis.setJSON(`service:${serviceId}`, data, ttl);
  },

  async getServiceData(serviceId: string): Promise<any | null> {
    return await redis.getJSON(`service:${serviceId}`);
  },

  async invalidateServiceCache(serviceId: string): Promise<boolean> {
    return await redis.del(`service:${serviceId}`);
  },
};

export default redis;
