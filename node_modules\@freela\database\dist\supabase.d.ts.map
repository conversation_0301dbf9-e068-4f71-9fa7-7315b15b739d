{"version": 3, "file": "supabase.d.ts", "sourceRoot": "", "sources": ["../src/supabase.ts"], "names": [], "mappings": "AAGA,OAAO,EAAgB,cAAc,EAAE,MAAM,uBAAuB,CAAC;AACrE,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAQ5C,eAAO,MAAM,QAAQ,EAAE,cAAc,CAAC,QAAQ,CAW5C,CAAC;AAGH,eAAO,MAAM,aAAa,EAAE,cAAc,CAAC,QAAQ,CAKjD,CAAC;AAGH,qBAAa,cAAc;IACzB,OAAO,CAAC,SAAS,CAAS;IAC1B,OAAO,CAAC,YAAY,CAAM;gBAEd,SAAS,EAAE,MAAM;IAKvB,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,WAAW,EAAE,WAAW,GAAE,MAAe;IAsB3F,mBAAmB,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,IAAI;IAiBpD,WAAW;IAOL,WAAW,CAAC,KAAK,GAAE,MAAW;IAiB9B,aAAa,CAAC,OAAO,EAAE,GAAG;CAkBjC;AAGD,qBAAa,mBAAmB;WAEjB,UAAU,CAAC,QAAQ,EAAE;QAChC,KAAK,EAAE,MAAM,CAAC;QACd,QAAQ,EAAE,MAAM,CAAC;QACjB,SAAS,EAAE,MAAM,CAAC;QAClB,QAAQ,EAAE,MAAM,CAAC;QACjB,IAAI,EAAE,QAAQ,GAAG,QAAQ,GAAG,OAAO,CAAC;QACpC,QAAQ,CAAC,EAAE,MAAM,CAAC;KACnB;;;;WAwBY,gBAAgB;;;;WAiBhB,qBAAqB;WAwBrB,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG;CAe5D;AAGD,qBAAa,qBAAqB;WAEnB,gBAAgB,CAAC,MAAM,EAAE,MAAM;WAoB/B,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG;WAiBhD,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG;CAiB9D;AAGD,qBAAa,iBAAiB;WAEf,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,GAAG,QAAQ;WAsBpF,eAAe,CAAC,MAAM,EAAE,MAAM;WAgB9B,oBAAoB,CAAC,kBAAkB,EAAE,GAAG;WAgB5C,sBAAsB,CAAC,MAAM,EAAE,MAAM;CAenD;AAED,eAAe,QAAQ,CAAC"}